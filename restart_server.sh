#!/bin/bash

# 🔄 Quick Server Restart Script for Manus Crawler

echo "🔄 Restarting Manus Crawler Server..."

# Kill existing server processes
echo "🛑 Stopping existing server..."
lsof -ti:8000 | xargs kill -9 2>/dev/null || true
pkill -f "python.*run.py" 2>/dev/null || true

# Wait a moment
sleep 2

# Start server
echo "🚀 Starting server..."
cd "$(dirname "$0")"
python3 run.py &

# Wait for server to start
echo "⏳ Waiting for server to start..."
sleep 3

# Test server
echo "🧪 Testing server..."
if curl -s -X GET "http://localhost:8000/health" > /dev/null; then
    echo "✅ Server is running successfully!"
    echo "🌐 Access points:"
    echo "   - Main UI: http://localhost:8000/ui"
    echo "   - Admin Panel: http://localhost:8000/admin"
    echo "   - API Docs: http://localhost:8000/docs"
    echo "   - Health Check: http://localhost:8000/health"
    
    # Test interactive chat endpoint
    echo ""
    echo "🧪 Testing Interactive Chat endpoint..."
    RESPONSE=$(curl -s -X POST "http://localhost:8000/chat-with-manus-realtime/" \
        -H "Content-Type: application/json" \
        -d '{"message": "test", "task_url": "https://manus.im/app", "request_id": "test-123", "headless": true}')
    
    if echo "$RESPONSE" | grep -q "started"; then
        echo "✅ Interactive Chat endpoint is working!"
        echo "💬 Response: $RESPONSE"
    else
        echo "❌ Interactive Chat endpoint failed"
        echo "📝 Response: $RESPONSE"
    fi
    
else
    echo "❌ Server failed to start!"
    echo "🔍 Check the terminal for error messages"
    exit 1
fi

echo ""
echo "🎉 Server restart completed!"
echo "📖 If you encounter issues, check TROUBLESHOOTING.md"
