"""
Smart Chrome Profile Manager for Manus Crawler
Tự động chọn profile phù hợp dựa trên loại hoạt động
"""

import os
import json
import asyncio
from typing import Optional, Dict, Any, List, Tuple
from pathlib import Path
from datetime import datetime, timedelta

from .config import settings
from ..models.schemas import ActivityType, ProfileSelectionStrategy


class ProfileInfo:
    """Thông tin về một Chrome profile"""
    
    def __init__(self, name: str, path: str):
        self.name = name
        self.path = path
        self.created_time = self._get_created_time()
        self.last_used = self._get_last_used()
        self.is_logged_in = self._check_login_status()
        self.size_mb = self._calculate_size()
        
    def _get_created_time(self) -> datetime:
        """Lấy thời gian tạo profile"""
        try:
            stat = os.stat(self.path)
            return datetime.fromtimestamp(stat.st_ctime)
        except:
            return datetime.now()
    
    def _get_last_used(self) -> datetime:
        """L<PERSON>y thời gian sử dụng cuối cùng"""
        try:
            # Check for recent files in profile
            recent_files = [
                "Current Session", "Current Tabs", "Last Session", "Last Tabs"
            ]
            latest_time = self.created_time
            
            for file_name in recent_files:
                file_path = os.path.join(self.path, file_name)
                if os.path.exists(file_path):
                    mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                    if mtime > latest_time:
                        latest_time = mtime
            
            return latest_time
        except:
            return self.created_time
    
    def _check_login_status(self) -> bool:
        """Kiểm tra xem profile có đăng nhập Manus không"""
        try:
            # Check for cookies or session data indicating login
            cookies_path = os.path.join(self.path, "Cookies")
            if os.path.exists(cookies_path) and os.path.getsize(cookies_path) > 1024:
                return True
            
            # Check for Local Storage data
            local_storage_path = os.path.join(self.path, "Local Storage")
            if os.path.exists(local_storage_path):
                return True
                
            return False
        except:
            return False
    
    def _calculate_size(self) -> float:
        """Tính size của profile (MB)"""
        try:
            total_size = 0
            for dirpath, dirnames, filenames in os.walk(self.path):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    if os.path.exists(filepath):
                        total_size += os.path.getsize(filepath)
            return round(total_size / (1024 * 1024), 1)
        except:
            return 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "name": self.name,
            "path": self.path,
            "created_time": self.created_time.isoformat(),
            "last_used": self.last_used.isoformat(),
            "is_logged_in": self.is_logged_in,
            "size_mb": self.size_mb,
            "age_days": (datetime.now() - self.created_time).days
        }


class SmartProfileManager:
    """Quản lý profile thông minh"""
    
    def __init__(self):
        self.base_path = settings.CHROME_PROFILE_BASE_PATH
        self._ensure_base_path()
        
    def _ensure_base_path(self):
        """Đảm bảo thư mục base path tồn tại"""
        Path(self.base_path).mkdir(parents=True, exist_ok=True)
    
    def get_all_profiles(self) -> List[ProfileInfo]:
        """Lấy danh sách tất cả profiles"""
        profiles = []
        
        try:
            if os.path.exists(self.base_path):
                for item in os.listdir(self.base_path):
                    item_path = os.path.join(self.base_path, item)
                    if os.path.isdir(item_path):
                        profiles.append(ProfileInfo(item, item_path))
        except Exception as e:
            print(f"Error getting profiles: {e}")
        
        return profiles
    
    def get_logged_in_profiles(self) -> List[ProfileInfo]:
        """Lấy danh sách profiles đã đăng nhập"""
        all_profiles = self.get_all_profiles()
        return [p for p in all_profiles if p.is_logged_in]
    
    def get_best_profile_for_activity(
        self, 
        activity_type: ActivityType,
        conversation_id: Optional[str] = None
    ) -> Optional[ProfileInfo]:
        """
        Chọn profile tốt nhất cho loại hoạt động
        
        Args:
            activity_type: Loại hoạt động
            conversation_id: ID cuộc hội thoại (cho continue_chat)
            
        Returns:
            ProfileInfo tốt nhất hoặc None
        """
        if activity_type == ActivityType.CRAWL_DATA:
            # Cho crawl data, không cần profile đăng nhập
            return None
            
        elif activity_type == ActivityType.NEW_CHAT:
            # Cho chat mới, cần profile đã đăng nhập, ưu tiên mới nhất
            logged_in_profiles = self.get_logged_in_profiles()
            if not logged_in_profiles:
                return None
            
            # Sắp xếp theo thời gian sử dụng gần nhất
            logged_in_profiles.sort(key=lambda p: p.last_used, reverse=True)
            return logged_in_profiles[0]
            
        elif activity_type == ActivityType.CONTINUE_CHAT:
            # Cho tiếp tục chat, tìm profile đã dùng cho conversation này
            if conversation_id:
                # TODO: Implement conversation tracking
                pass
            
            # Fallback: dùng profile đã đăng nhập gần nhất
            logged_in_profiles = self.get_logged_in_profiles()
            if not logged_in_profiles:
                return None
                
            logged_in_profiles.sort(key=lambda p: p.last_used, reverse=True)
            return logged_in_profiles[0]
        
        return None
    
    def select_profile(
        self,
        strategy: ProfileSelectionStrategy,
        activity_type: ActivityType,
        manual_profile_name: Optional[str] = None,
        conversation_id: Optional[str] = None
    ) -> Tuple[Optional[str], bool, Dict[str, Any]]:
        """
        Chọn profile dựa trên strategy
        
        Returns:
            Tuple of (profile_name, use_system_profile, selection_info)
        """
        selection_info = {
            "strategy": strategy.value,
            "activity_type": activity_type.value,
            "timestamp": datetime.now().isoformat()
        }
        
        if strategy == ProfileSelectionStrategy.NONE:
            selection_info["selected"] = "none"
            return None, False, selection_info
            
        elif strategy == ProfileSelectionStrategy.SYSTEM:
            selection_info["selected"] = "system"
            return None, True, selection_info
            
        elif strategy == ProfileSelectionStrategy.MANUAL:
            if manual_profile_name:
                selection_info["selected"] = f"manual:{manual_profile_name}"
                return manual_profile_name, False, selection_info
            else:
                # Fallback to auto if no manual profile specified
                strategy = ProfileSelectionStrategy.AUTO
        
        if strategy == ProfileSelectionStrategy.AUTO:
            best_profile = self.get_best_profile_for_activity(activity_type, conversation_id)
            
            if best_profile:
                selection_info.update({
                    "selected": f"auto:{best_profile.name}",
                    "profile_info": best_profile.to_dict()
                })
                return best_profile.name, False, selection_info
            else:
                # No suitable profile found
                if activity_type == ActivityType.CRAWL_DATA:
                    selection_info["selected"] = "auto:none (crawl_data)"
                    return None, False, selection_info
                else:
                    selection_info.update({
                        "selected": "auto:none (no_logged_in_profiles)",
                        "warning": "No logged-in profiles found for chat activity"
                    })
                    return None, False, selection_info
        
        return None, False, selection_info
    
    def create_profile_if_needed(self, profile_name: str) -> bool:
        """Tạo profile nếu chưa tồn tại"""
        profile_path = os.path.join(self.base_path, profile_name)
        if not os.path.exists(profile_path):
            try:
                os.makedirs(profile_path, exist_ok=True)
                return True
            except Exception as e:
                print(f"Error creating profile {profile_name}: {e}")
                return False
        return True


# Global instance
profile_manager = SmartProfileManager()
