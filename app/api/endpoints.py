"""
API endpoints for Manus Crawler
"""

import os
import asyncio
from typing import Dict, Any
from fastapi import APIRouter, HTTPEx<PERSON>, Depends, Header
from fastapi.responses import HTMLResponse, FileResponse

from ..models.schemas import (
    CrawlUrlRequest, CrawlUrlRequestWithId,
    CrawlHtmlRequest, CrawlHtmlRequestWithId,
    SetupProfileRequest, StandardResponse,
    HealthResponse, ProfileListResponse,
    ChatWithManusRequest
)
from ..core.crawler import crawl_manus_page_content, setup_chrome_profile_interactive, chat_with_manus_interactive
from ..core.config import settings

router = APIRouter()

# API Key validation
def verify_api_key(x_api_key: str = Header(None, alias="X-API-KEY")):
    if x_api_key != settings.ADMIN_API_KEY:
        raise HTTPException(status_code=401, detail="Invalid API Key")
    return x_api_key

@router.get("/", response_class=HTMLResponse)
async def root():
    return """
    <html>
        <head>
            <title>Manus Crawler API</title>
            <style>
                body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
                .card { background: #f8f9fa; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #007bff; }
                .admin-card { border-left-color: #dc3545; }
                a { color: #007bff; text-decoration: none; font-weight: bold; }
                a:hover { text-decoration: underline; }
                .warning { background: #fff3cd; border-left-color: #ffc107; color: #856404; }
            </style>
        </head>
        <body>
            <h1>🕷️ Manus Crawler API</h1>
            <p>Ứng dụng FastAPI để crawl dữ liệu từ Manus.im với Playwright và realtime updates</p>

            <div class="card">
                <h3>👤 User Interface</h3>
                <p>Giao diện chính để crawl dữ liệu với realtime updates</p>
                <a href="/ui">🚀 Mở Giao diện Crawl</a>
            </div>

            <div class="card admin-card">
                <h3>🔐 Admin Panel</h3>
                <p>Quản lý Chrome profiles và cấu hình hệ thống (cần API key)</p>
                <a href="/admin">⚙️ Mở Admin Panel</a>
            </div>

            <div class="card">
                <h3>📚 API Documentation</h3>
                <p>Swagger UI với tất cả endpoints và schemas</p>
                <a href="/docs">📖 Xem API Docs</a>
            </div>

            <div class="card">
                <h3>💚 Health Check</h3>
                <p>Kiểm tra trạng thái server</p>
                <a href="/health">🔍 Health Status</a>
            </div>

            <div class="card warning">
                <h3>⚠️ Lưu ý</h3>
                <ul>
                    <li>Admin panel yêu cầu API key để truy cập</li>
                    <li>Setup Chrome profile cần tương tác thủ công</li>
                    <li>Tuân thủ robots.txt và terms of service</li>
                </ul>
            </div>
        </body>
    </html>
    """

@router.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    return HealthResponse(
        status="healthy",
        message="Manus Crawler API is running"
    )

@router.get("/ui", response_class=HTMLResponse)
async def get_realtime_ui_page():
    """Phục vụ trang HTML cho giao diện realtime."""
    html_file_path = settings.TEMPLATES_DIR + "/index.html"
    if os.path.exists(html_file_path):
        return FileResponse(html_file_path)
    raise HTTPException(status_code=404, detail="Không tìm thấy file index.html")

@router.get("/admin", response_class=HTMLResponse)
async def get_admin_page():
    """Phục vụ trang HTML cho admin panel."""
    html_file_path = settings.TEMPLATES_DIR + "/admin.html"
    if os.path.exists(html_file_path):
        return FileResponse(html_file_path)
    raise HTTPException(status_code=404, detail="Không tìm thấy file admin.html")

@router.post("/crawl-url/", response_model=Dict[str, Any])
async def crawl_url(request: CrawlUrlRequest):
    """Crawl URL và trả về dữ liệu với smart profile selection."""
    result = await crawl_manus_page_content(
        url=request.url,
        profile_name=request.profile_name,
        use_system_profile=request.use_system_profile,
        headless=request.headless,
        activity_type=request.activity_type,
        profile_strategy=request.profile_strategy
    )
    return result

@router.post("/crawl-html/", response_model=Dict[str, Any])
async def crawl_html(request: CrawlHtmlRequest):
    """Parse HTML content và trả về dữ liệu (không realtime)."""
    result = await crawl_manus_page_content(
        html_content=request.html_content,
        profile_name=request.profile_name,
        use_system_profile=request.use_system_profile,
        headless=request.headless
    )
    return result

# Admin endpoints
@router.post("/admin/setup-chrome-profile/", dependencies=[Depends(verify_api_key)])
async def admin_setup_chrome_profile(request: SetupProfileRequest):
    """
    Endpoint admin để thiết lập Chrome profile.
    Yêu cầu API Key trong header X-API-KEY.
    """
    result = await setup_chrome_profile_interactive(
        profile_name=request.profile_name,
        url=request.url
    )
    return result

@router.get("/admin/list-profiles/", response_model=ProfileListResponse, dependencies=[Depends(verify_api_key)])
async def admin_list_profiles():
    """
    Endpoint admin để liệt kê các Chrome profiles.
    Yêu cầu API Key trong header X-API-KEY.
    """
    from datetime import datetime

    chrome_profiles_path = settings.CHROME_PROFILE_BASE_PATH
    profiles = []

    try:
        if os.path.exists(chrome_profiles_path):
            for item in os.listdir(chrome_profiles_path):
                item_path = os.path.join(chrome_profiles_path, item)
                if os.path.isdir(item_path):
                    # Lấy thông tin profile
                    stat = os.stat(item_path)
                    created_time = datetime.fromtimestamp(stat.st_ctime).strftime("%Y-%m-%d %H:%M")

                    # Tính size thư mục (đơn giản)
                    total_size = 0
                    try:
                        for dirpath, dirnames, filenames in os.walk(item_path):
                            for filename in filenames:
                                filepath = os.path.join(dirpath, filename)
                                if os.path.exists(filepath):
                                    total_size += os.path.getsize(filepath)
                    except:
                        total_size = 0

                    # Chuyển đổi size sang MB
                    size_mb = round(total_size / (1024 * 1024), 1)

                    profiles.append({
                        "name": item,
                        "created": created_time,
                        "size": f"{size_mb}MB",
                        "path": item_path
                    })

        return ProfileListResponse(
            success=True,
            profiles=profiles,
            total=len(profiles),
            base_path=chrome_profiles_path
        )

    except Exception as e:
        return ProfileListResponse(
            success=False,
            error=str(e),
            profiles=[],
            total=0
        )

@router.delete("/admin/delete-profile/{profile_name}", dependencies=[Depends(verify_api_key)])
async def admin_delete_profile(profile_name: str):
    """
    Endpoint admin để xóa Chrome profile.
    Yêu cầu API Key trong header X-API-KEY.
    """
    import shutil

    chrome_profiles_path = settings.CHROME_PROFILE_BASE_PATH
    profile_path = os.path.join(chrome_profiles_path, profile_name)

    try:
        if os.path.exists(profile_path) and os.path.isdir(profile_path):
            shutil.rmtree(profile_path)
            return {
                "success": True,
                "message": f"Profile '{profile_name}' đã được xóa thành công",
                "profile_name": profile_name
            }
        else:
            return {
                "success": False,
                "message": f"Profile '{profile_name}' không tồn tại",
                "profile_name": profile_name
            }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": f"Lỗi khi xóa profile '{profile_name}': {str(e)}",
            "profile_name": profile_name
        }

@router.post("/chat-with-manus-realtime/")
async def chat_with_manus_realtime(request: ChatWithManusRequest):
    """
    Gửi message lên Manus.im và crawl response realtime
    """
    try:
        # Import manager từ websocket module
        from .websocket import manager

        # Bắt đầu chat session với smart profile selection
        result = await chat_with_manus_interactive(
            message=request.message,
            task_url=request.task_url,
            profile_name=request.profile_name,
            headless=request.headless,
            websocket_callback=lambda req_id, msg: manager.send_to_request_id(req_id, msg),
            request_id=request.request_id,
            activity_type=request.activity_type,
            profile_strategy=request.profile_strategy,
            conversation_id=request.conversation_id
        )

        # Gửi kết quả qua WebSocket
        if result["success"]:
            await manager.send_data_to_request_id(request.request_id, result)
        else:
            error_message = result.get("error", "Unknown error")
            error_data = {k: v for k, v in result.items() if k != "error"}  # Include all other data for debugging
            await manager.send_error_to_request_id(request.request_id, error_message, error_data)

        return {"status": "started", "request_id": request.request_id}
    except Exception as e:
        # Import manager nếu chưa có
        try:
            from .websocket import manager
            await manager.send_error_to_request_id(request.request_id, str(e))
        except:
            pass
        raise HTTPException(status_code=500, detail=str(e))
