# 🚀 Manus Crawler - COMPLETE IMPLEMENTATION

Ứng dụng FastAPI để crawl dữ liệu từ trang web Manus.im với cấu trúc code được tổ chức lại chuyên nghiệp.

## ✅ **NEWLY IMPLEMENTED: Interactive Chat với Manus.im**

**🎉 HOÀN THÀNH** - Chức năng Interactive Chat đã được implement đầy đủ! Bây giờ bạn có thể:
- 💬 Chat trực tiếp với Manus.im qua API
- 🔄 Nhận realtime updates qua WebSocket
- 📊 Crawl updated page data sau mỗi chat
- 📎 Detect và extract file attachments
- 🔐 Sử dụng Chrome profiles cho authentication

## ✨ Tính năng chính

- 🌐 **Web Scraping**: Crawl dữ liệu từ Manus.im với Playwright
- 🚀 **FastAPI Backend**: RESTful API với WebSocket support
- 🔄 **Realtime Updates**: Cập nhật tiến trình crawl qua WebSocket
- 🎭 **Chrome Profile Management**: Quản lý multiple Chrome profiles
- 📊 **Admin Panel**: Giao diện quản trị web-based
- 🏷️ **Message Classification**: Tự động phân loại message types (Text, Code, List, File, Mixed)
- 🐳 **Docker Support**: Containerization hoàn chỉnh
- 🧪 **Comprehensive Testing**: Unit tests và integration tests

## 📁 Cấu trúc dự án mới

```
manus_crawler/
├── 📁 app/                          # Main application code
│   ├── __init__.py
│   ├── main.py                      # FastAPI app entry point
│   ├── 📁 api/                      # API routes
│   │   ├── __init__.py
│   │   ├── endpoints.py             # API endpoints
│   │   └── websocket.py             # WebSocket handlers
│   ├── 📁 core/                     # Core functionality
│   │   ├── __init__.py
│   │   ├── config.py                # Configuration
│   │   ├── crawler.py               # Crawler logic
│   │   └── selectors.py             # CSS selectors
│   ├── 📁 models/                   # Pydantic models
│   │   ├── __init__.py
│   │   └── schemas.py               # Request/Response models
│   └── 📁 static/                   # Static files
│       └── 📁 templates/            # HTML templates
│           ├── index.html
│           └── admin.html
├── 📁 tests/                        # Test files
│   ├── __init__.py
│   ├── test_api.py
│   ├── test_crawler.py
│   └── test_admin.py
├── 📁 scripts/                      # Utility scripts
│   ├── setup.sh
│   ├── setup.bat
│   └── demo_html_parser.py
├── 📁 docs/                         # Documentation
│   ├── README.md
│   ├── QUICKSTART.md
│   ├── ADMIN_PANEL_COMPLETE.md
│   └── SINGLETON_LOCK_FIX.md
├── 📁 data/                         # Data files
│   ├── html-manus.example.html
│   └── chrome_profiles/             # Chrome profiles
├── 📁 docker/                       # Docker files
│   ├── Dockerfile
│   └── docker-compose.yml
├── .env                             # Environment variables
├── .gitignore
├── requirements.txt
└── run.py                           # Application runner
```

## 🚀 Quick Start

### 🆕 **Interactive Chat với Manus.im**

#### **1. Setup Chrome Profile cho Chat**
```bash
# Truy cập Admin Panel
http://localhost:8000/admin

# Nhập API key và setup profile
API Key: your_super_secret_key_here
Profile Name: manus_login_profile
URL: https://manus.im/

# Đăng nhập thủ công vào Manus.im trong browser được mở
```

#### **2. Sử dụng Interactive Chat**
```bash
# Truy cập User Interface
http://localhost:8000/ui

# Scroll xuống phần "Interactive Chat với Manus"
# Điền thông tin:
- Task URL: https://manus.im/app/your-task-id
- Message: "Hello, can you help me with Python?"
- Profile: manus_login_profile

# Nhấn "Bắt đầu Chat với Manus"
# Xem realtime progress và results!
```

#### **3. API Usage cho Chat**
```bash
curl -X POST "http://localhost:8000/chat-with-manus-realtime/" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Hello Manus!",
    "task_url": "https://manus.im/app/task-id",
    "profile_name": "manus_login_profile",
    "request_id": "uuid-here",
    "headless": true
  }'
```

### Method 1: Using the new structure
```bash
# Install dependencies
pip install -r requirements.txt
playwright install chromium

# Run the application
python run.py
```

### Method 2: Using scripts
```bash
# Linux/macOS
./scripts/setup.sh

# Windows
scripts/setup.bat
```

### Method 3: Using Docker
```bash
cd docker
docker-compose up --build
```

## 🎯 Access Points

- **Main Interface**: http://localhost:8000/ui
- **Admin Panel**: http://localhost:8000/admin
- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health

## 🔧 Configuration

Environment variables in `.env`:
```env
ADMIN_API_KEY=your_super_secret_key_here
CHROME_PROFILE_BASE_PATH=./data/chrome_profiles
DEBUG=false
HOST=0.0.0.0
PORT=8000
```

## 🧪 Testing

```bash
# Run core tests
python tests/test_api.py
python tests/test_app.py
python tests/test_websocket.py

# Test Interactive Chat
python3 test_interactive_chat.py

# Run demo
python demo.py
```

## 🔧 Troubleshooting

### ❌ **Lỗi 404 cho Interactive Chat**
```bash
# Quick fix: Restart server
./restart_server.sh

# Or manual restart:
lsof -ti:8000 | xargs kill -9
python3 run.py
```

### ✅ **Verify Installation**
```bash
# Test server health
curl -X GET "http://localhost:8000/health"

# Test interactive chat endpoint
curl -X POST "http://localhost:8000/chat-with-manus-realtime/" \
  -H "Content-Type: application/json" \
  -d '{"message": "test", "task_url": "https://manus.im/app", "request_id": "test-123", "headless": true}'
```

📖 **Detailed troubleshooting guide**: See `TROUBLESHOOTING.md`

## 🚀 Quick Start

### 1. Setup
```bash
# Clone repository
git clone <repository-url>
cd Youhome-2

# Run setup script
chmod +x setup.sh
./setup.sh

# Or manual setup:
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
playwright install chromium
```

### 2. Run Application
```bash
# Activate virtual environment
source venv/bin/activate

# Start server
python run.py

# Open browser
open http://localhost:8000
```

### 3. API Usage
```bash
# Crawl HTML content
curl -X POST "http://localhost:8000/crawl-html/" \
  -H "Content-Type: application/json" \
  -d '{
    "html_content": "<div data-event-id=\"test\">...</div>",
    "headless": true
  }'

# Crawl URL (requires login)
curl -X POST "http://localhost:8000/crawl-url/" \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://manus.im/chat/...",
    "profile_name": "manus_profile",
    "headless": true
  }'
```

### 4. Demo
```bash
# Run demo with example HTML
python demo.py
```

## 🏷️ Message Classification

API response now includes automatic message classification for Manus AI responses:

```json
{
  "success": true,
  "data": {
    "chat_messages": [
      {
        "event_id": "...",
        "type": "manus",
        "manus_message": "...",
        "manus_html": "...",
        "message_subtype": "code",
        "content_analysis": {
          "text_length": 186,
          "code_blocks_count": 1,
          "detected_languages": ["python"],
          "has_lists": false,
          "file_references": [".py", ".json"]
        }
      }
    ]
  }
}
```

### Message Types:
- **📝 text**: Simple text responses
- **💻 code**: Contains code snippets
- **📋 list**: Lists and step-by-step guides
- **📁 file**: Multiple file references
- **🔀 mixed**: Complex content with multiple types

## 🎉 Benefits of New Structure

✅ **Modular Design** - Separated concerns into logical modules
✅ **Scalable Architecture** - Easy to add new features
✅ **Better Testing** - Organized test structure
✅ **Clean Configuration** - Centralized settings
✅ **Professional Layout** - Industry standard structure
✅ **Easy Deployment** - Docker support with proper paths

## 🔄 Migration from Old Structure

The application has been completely restructured for better maintainability:

- **Old**: All files in root directory
- **New**: Organized into logical packages
- **Benefits**: Better imports, easier testing, cleaner code

All functionality remains the same, just better organized! 🎯
