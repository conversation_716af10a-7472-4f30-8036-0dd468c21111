#!/usr/bin/env python3
"""
Test script to verify WebSocket error handling improvements
"""

import asyncio
import json
from app.api.websocket import ConnectionManager
from app.models.schemas import WebSocketMessage

async def test_websocket_error_handling():
    """Test the enhanced WebSocket error handling"""
    print("🧪 TESTING WEBSOCKET ERROR HANDLING...")
    
    # Create a connection manager
    manager = ConnectionManager()
    
    # Test error message with data
    test_request_id = "test-123"
    error_message = "Chat interface not found on this page"
    error_data = {
        "current_url": "https://manus.im/app/test",
        "page_title": "Test Page",
        "debug_info": {
            "textareas_found": 0,
            "inputs_found": 5,
            "contenteditable_found": 1
        },
        "chat_input_selector": "textarea[placeholder='Send message to <PERSON><PERSON>']"
    }
    
    # Create WebSocket message
    try:
        websocket_message = WebSocketMessage(
            type="error", 
            message=error_message, 
            data=error_data
        )
        message_json = websocket_message.model_dump_json()
        
        print("✅ WebSocket error message created successfully")
        print(f"📝 Message: {json.dumps(json.loads(message_json), indent=2)}")
        
        # Verify the message structure
        parsed = json.loads(message_json)
        assert parsed["type"] == "error"
        assert parsed["message"] == error_message
        assert parsed["data"]["current_url"] == "https://manus.im/app/test"
        assert parsed["data"]["debug_info"]["textareas_found"] == 0
        
        print("✅ Message structure validation passed")
        
    except Exception as e:
        print(f"❌ Error creating WebSocket message: {e}")
        return False
    
    print("🎉 WEBSOCKET ERROR HANDLING TEST PASSED!")
    return True

async def test_websocket_message_types():
    """Test different WebSocket message types"""
    print("\n🧪 TESTING WEBSOCKET MESSAGE TYPES...")
    
    # Test progress message
    try:
        progress_msg = WebSocketMessage(type="progress", message="Looking for chat input...")
        print("✅ Progress message created")
    except Exception as e:
        print(f"❌ Progress message failed: {e}")
        return False
    
    # Test data message
    try:
        data_msg = WebSocketMessage(type="data", data={"test": "data"})
        print("✅ Data message created")
    except Exception as e:
        print(f"❌ Data message failed: {e}")
        return False
    
    # Test error message with data
    try:
        error_msg = WebSocketMessage(
            type="error", 
            message="Test error", 
            data={"debug": "info"}
        )
        print("✅ Error message with data created")
    except Exception as e:
        print(f"❌ Error message with data failed: {e}")
        return False
    
    print("🎉 ALL MESSAGE TYPES TEST PASSED!")
    return True

if __name__ == "__main__":
    async def main():
        print("🚀 RUNNING WEBSOCKET TESTS...")
        
        success = True
        
        # Test error handling
        if not await test_websocket_error_handling():
            success = False
        
        # Test message types
        if not await test_websocket_message_types():
            success = False
        
        if success:
            print(f"\n✅ ALL WEBSOCKET TESTS PASSED!")
            print(f"🔧 Enhanced error handling is working correctly!")
        else:
            print(f"\n❌ SOME WEBSOCKET TESTS FAILED!")
            return 1
        
        return 0
    
    import sys
    sys.exit(asyncio.run(main()))
