# 🔧 Troubleshooting Guide - Interactive Chat

## ❌ **Lỗi 404 "HTTP error! status: 404"**

### 🔍 **<PERSON><PERSON><PERSON><PERSON> nhân:**
- Server ch<PERSON><PERSON> đ<PERSON> restart sau khi thêm endpoint mới
- Browser cache cũ
- Server không chạy đúng port

### ✅ **Giải pháp:**

#### **1. Restart Server**
```bash
# Kill existing server
lsof -ti:8000 | xargs kill -9

# Restart server
cd /Users/<USER>/Documents/augment-projects/Youhome-2
python3 run.py
```

#### **2. Verify Server Status**
```bash
# Check if server is running
curl -X GET "http://localhost:8000/health"
# Expected: {"status":"healthy","message":"Manus Crawler API is running"}

# Test interactive chat endpoint
curl -X POST "http://localhost:8000/chat-with-manus-realtime/" \
  -H "Content-Type: application/json" \
  -d '{"message": "test", "task_url": "https://manus.im/app", "request_id": "test-123", "headless": true}'
# Expected: {"status":"started","request_id":"test-123"}
```

#### **3. Clear Browser Cache**
```bash
# Hard refresh browser
Ctrl+F5 (Windows/Linux) or Cmd+Shift+R (Mac)

# Or open incognito/private window
```

#### **4. Check Frontend**
```bash
# Access UI
http://localhost:8000/ui

# Check if Interactive Chat section is visible
# Should see form with Task URL, Message, Profile inputs
```

## ⚠️ **Lỗi khác có thể gặp:**

### **1. WebSocket Connection Failed**
```bash
# Symptoms: "WebSocket connection failed"
# Solution: Restart server và refresh browser
```

### **2. Chrome Profile Not Found**
```bash
# Symptoms: "Profile not found" error
# Solution: Setup Chrome profile trước:
# 1. Go to http://localhost:8000/admin
# 2. Enter API key: your_super_secret_key_here
# 3. Setup profile với Manus.im URL
```

### **3. Task URL Invalid**
```bash
# Symptoms: "Chat interface not found"
# Solution: Sử dụng valid Manus task URL:
# Format: https://manus.im/app/your-task-id
# NOT: https://manus.im/app (general URL)
```

### **4. Login Required**
```bash
# Symptoms: "Not logged in" error
# Solution: 
# 1. Setup Chrome profile
# 2. Login manually vào Manus.im trong browser được mở
# 3. Save profile sau khi login
```

## 🧪 **Testing Steps:**

### **Step 1: Basic Server Test**
```bash
curl -X GET "http://localhost:8000/health"
# Expected: 200 OK với health status
```

### **Step 2: Endpoint Test**
```bash
curl -X POST "http://localhost:8000/chat-with-manus-realtime/" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Hello",
    "task_url": "https://manus.im/app/test",
    "request_id": "test-123",
    "headless": true
  }'
# Expected: {"status":"started","request_id":"test-123"}
```

### **Step 3: Frontend Test**
```bash
# Open browser
http://localhost:8000/ui

# Check Interactive Chat section
# Fill form và test
```

### **Step 4: WebSocket Test**
```bash
# Open browser console
# Check WebSocket connection logs
# Should see: "WebSocket connected for request: ..."
```

## 🔄 **Complete Reset Procedure:**

### **If everything fails, do complete reset:**

```bash
# 1. Kill all processes
lsof -ti:8000 | xargs kill -9
pkill -f "python.*run.py"

# 2. Clean restart
cd /Users/<USER>/Documents/augment-projects/Youhome-2
python3 run.py

# 3. Wait for server startup
# Look for: "Uvicorn running on http://0.0.0.0:8000"

# 4. Test health
curl -X GET "http://localhost:8000/health"

# 5. Test endpoint
curl -X POST "http://localhost:8000/chat-with-manus-realtime/" \
  -H "Content-Type: application/json" \
  -d '{"message": "test", "task_url": "https://manus.im/app", "request_id": "test-123", "headless": true}'

# 6. Open fresh browser window
http://localhost:8000/ui
```

## 📋 **Checklist khi gặp lỗi:**

- [ ] Server đang chạy trên port 8000
- [ ] Health endpoint trả về 200 OK
- [ ] Interactive chat endpoint trả về 200 OK
- [ ] Browser đã clear cache
- [ ] Chrome profile đã được setup
- [ ] Task URL là valid Manus task URL
- [ ] WebSocket connection thành công

## 🆘 **Nếu vẫn không hoạt động:**

1. **Check server logs** trong terminal chạy `python3 run.py`
2. **Check browser console** cho JavaScript errors
3. **Verify file structure** - đảm bảo tất cả files đã được tạo đúng
4. **Test basic endpoints** trước khi test interactive chat

## ✅ **Expected Behavior:**

Khi hoạt động đúng, bạn sẽ thấy:

1. **Frontend**: Form với Task URL, Message, Profile inputs
2. **WebSocket**: "WebSocket connected" message
3. **Progress**: Realtime progress updates
4. **Results**: Chat response và updated page data
5. **No Errors**: Không có 404, 500, hoặc connection errors

**🎯 Interactive Chat functionality is working correctly after server restart!**
